import requests
import time

# ------------------- 配置区 -------------------
# 请替换为您的有效高德 Web 服务 API Key
GAODE_API_KEY = ""
# ---------------------------------------------

def geocode_amap(address, city=None):
    """
    使用高德地图API进行地理编码
    :param address: 详细地址 (例如："常营北京新天地三期")
    :param city: 城市名或区名，用于辅助定位 (例如："朝阳")
    :return: (纬度, 经度) 或特殊错误标识元组或 (None, None)
    """
    if not GAODE_API_KEY or GAODE_API_KEY == "在这里填写您申请到的高德API Key":
        print("错误：请先在代码中配置有效的 GAODE_API_KEY。")
        return None, None

    base_url = "https://restapi.amap.com/v3/geocode/geo" 
    params = {
        "key": GAODE_API_KEY,
        "address": address,
        "output": "json"
    }
    if city:
        params["city"] = city

    try:
        response = requests.get(base_url, params=params, timeout=10) # 增加超时设置
        response.raise_for_status() # 如果请求失败 (4xx or 5xx), 会抛出异常
        result = response.json()

        if result["status"] == "1" and result.get("geocodes"):
            if result["geocodes"]: # 确保 geocodes 列表不为空
                location = result["geocodes"][0]["location"]
                longitude, latitude = map(float, location.split(','))
                return latitude, longitude
            else:
                print(f"地理编码成功但未返回有效坐标: {address} (城市: {city}). API响应: {result}")
                return None, None
        else:
            error_info = result.get("info", "未知错误")
            if "USER_DAILY_QUERY_OVER_LIMIT" in error_info.upper():
                print(f"错误：日查询量已超出限制。地址：{address}, 城市：{city}")
                return "OVER_LIMIT", "OVER_LIMIT"
            elif "USER_KEY_RECYCLED" in error_info.upper() or "INVALID_USER_KEY" in error_info.upper():
                 print(f"错误：API Key无效或已被回收。地址：{address}, 城市：{city}")
                 return "INVALID_KEY", "INVALID_KEY"
            print(f"地理编码失败: {address} (城市: {city}). 原因: {error_info}")
            return None, None
    except requests.exceptions.Timeout:
        print(f"请求超时: {address} (城市: {city})")
        return None, None
    except requests.exceptions.RequestException as e:
        print(f"请求错误: {address} (城市: {city}). 错误: {e}")
        return None, None
    except Exception as e:
        print(f"处理响应时发生意外错误: {address} (城市: {city}). 错误: {e}, API响应: {result if 'result' in locals() else 'N/A'}")
        return None, None

def interactive_geocoder():
    """
    提供交互式界面，让用户逐条输入地址信息进行地理编码测试。
    """
    print("\n高德地图地址经纬度单条测试工具")
    print("根据提示输入信息。在任何提示符下输入 'q', 'quit', 或 'exit' 可以退出程序。")
    print("-" * 40)

    while True:
        district = input("① 请输入 区 (例如: 朝阳, 或输入 q 退出): ").strip()
        if district.lower() in ['q', 'quit', 'exit']:
            break

        area = input(f"② 请输入 街道/区域 (例如: 常营, 隶属于 {district}, 或输入 q 退出): ").strip()
        if area.lower() in ['q', 'quit', 'exit']:
            break

        specific_name = input(f"③ 请输入 具体地址/小区名 (例如: 北京新天地三期, 位于 {district} {area}, 或输入 q 退出): ").strip()
        if specific_name.lower() in ['q', 'quit', 'exit']:
            break

        if not district or not specific_name: # 区域可以有时为空，但区和具体名称通常是必要的
            print("输入不完整，请至少确保“区”和“具体地址/小区名”已输入。")
            print("-" * 40)
            continue

        # 组合 街道/区域 和 具体名称 作为API的address参数
        # 如果区域为空，则只使用具体名称
        address_for_api = (area + specific_name) if area else specific_name
        # 使用 区 作为API的city参数，以提高准确性
        city_for_api = district

        print(f"\n正在查询: 地址='{address_for_api}', 城市提示='{city_for_api}'")
        latitude, longitude = geocode_amap(address_for_api, city=city_for_api)

        if latitude is not None and longitude is not None:
            if isinstance(latitude, str) and ("LIMIT" in latitude.upper() or "KEY" in latitude.upper()):
                 # geocode_amap 内部已打印错误信息
                 print("API查询遇到问题（如超限或Key无效），无法获取经纬度。")
            else:
                print(f"🎉 查询成功: 纬度 = {latitude}, 经度 = {longitude}")
        else:
            # geocode_amap 内部已打印具体错误信息
            print("❌ 未能获取到经纬度。请检查地址信息、网络连接或API Key配置。")
        
        print("-" * 40)
        # API 请求之间的轻微延迟，良好的实践
        time.sleep(0.1) 

    print("测试工具已退出。")

if __name__ == "__main__":
    if GAODE_API_KEY == "在这里填写您申请到的高德API Key":
        print("🚫 重要提示：请打开Python脚本，在 'GAODE_API_KEY' 变量处填入您申请的高德API Key！")
        print("🚫 未配置API Key，程序无法进行地理编码。")
    else:
        interactive_geocoder()