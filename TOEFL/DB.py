#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TOEFL Reading & Listening Progress Tracker CLI

Features:
- Create users
- Interactive CLI with main and user contexts
- Support for modules: reading, listening (easily extended via CONFIG)
- Commands: create, go, add, min, list, reset, exit, back
- Persistent storage in 'progress.json'
- Easy to extend: update CONFIG at top with new modules/categories/subtopics
"""
import json
import os
import sys

# ----- Configuration -----
# Edit CONFIG to add/remove modules, categories, or subtopics (with total counts).
CONFIG = {
    "reading": {
        "Life Science": {
            "Animal Behavior": 19,
            "Animal & Environment": 13,
            "Zoology": 15,
            "Botany": 6,
            "Others": 1,
        },
        "Physical Science": {
            "Geology": 14,
            "Astronomy": 11,
            "Environmental & Ecology": 6,
            "Meteorology": 2,
            "Others": 6,
        },
        "Social Science": {
            "Sociology": 2,
            "History": 33,
            "Business & Economics": 19,
            "Psychology": 1,
            "Archaeology": 3,
            "Fine Arts": 22,
            "Agriculture": 11,
        },
    },
    "listening": {
        # Placeholder: add listening categories & subtopics here
        # "Section Name": {"Subtopic A": total_count, ...},
    },
}
DB_PATH = "progress.json"


class TOEFLTracker:
    def __init__(self, db_path=DB_PATH):
        self.db_path = db_path
        self.config = CONFIG
        self.progress = {"users": {}}
        self.load_progress()

    def load_progress(self):
        if os.path.exists(self.db_path):
            with open(self.db_path, "r", encoding="utf-8") as f:
                self.progress = json.load(f)
        else:
            self.save_progress()

    def save_progress(self):
        with open(self.db_path, "w", encoding="utf-8") as f:
            json.dump(self.progress, f, indent=4, ensure_ascii=False)

    def ensure_user(self, username):
        if username not in self.progress["users"]:
            print(f"[Error] User '{username}' not found.")
            return False
        return True

    def create_user(self, username):
        if username in self.progress["users"]:
            print(f"[Error] User '{username}' already exists.")
            return
        # Initialize new user with zero progress for all modules/subtopics
        self.progress["users"][username] = {}
        for module, cats in self.config.items():
            self.progress["users"][username][module] = {}
            for cat, subs in cats.items():
                for subtopic in subs:
                    self.progress["users"][username][module][subtopic] = 0
        self.save_progress()
        print(f"User '{username}' created.")

    def list_users(self):
        users = list(self.progress["users"].keys())
        if not users:
            print("[Info] No users found.")
        else:
            print("Existing users:")
            for u in users:
                print(f" - {u}")

    def reset_user(self, username):
        if not self.ensure_user(username):
            return
        for module in self.config:
            for sub in self.progress["users"][username][module]:
                self.progress["users"][username][module][sub] = 0
        self.save_progress()
        print(f"All progress for '{username}' has been reset.")

    def reset_subtopic(self, username, module, subtopic):
        if not self.ensure_user(username):
            return
        if module not in self.config:
            print(f"[Error] Module '{module}' does not exist.")
            return
        if subtopic not in self.progress["users"][username][module]:
            print(f"[Error] Subtopic '{subtopic}' not found in module '{module}'.")
            return
        self.progress["users"][username][module][subtopic] = 0
        self.save_progress()
        print(f"Progress cleared for '{subtopic}' ({module}) of user '{username}'.")

    def add_progress(self, username, module, subtopic, count):
        if not self.ensure_user(username):
            return
        total = self.config[module].get(subtopic)
        # For nested categories: flatten lookup
        if total is None:
            # try nested search
            for cats in self.config[module].values():
                if subtopic in cats:
                    total = cats[subtopic]
                    break
        if total is None:
            print(f"[Error] Subtopic '{subtopic}' not found in module '{module}'.")
            return
        current = self.progress["users"][username][module].get(subtopic, 0)
        new_val = min(current + count, total)
        self.progress["users"][username][module][subtopic] = new_val
        self.save_progress()
        print(f"Updated '{subtopic}': {current} -> {new_val} / {total}.")

    def subtract_progress(self, username, module, subtopic, count):
        if not self.ensure_user(username):
            return
        current = self.progress["users"][username][module].get(subtopic)
        if current is None:
            print(f"[Error] Subtopic '{subtopic}' not found in module '{module}'.")
            return
        new_val = max(current - count, 0)
        self.progress["users"][username][module][subtopic] = new_val
        self.save_progress()
        print(f"Updated '{subtopic}': {current} -> {new_val}.")

    def print_progress(self, username, module, subtopic=None):
        if not self.ensure_user(username):
            return
        user_data = self.progress["users"][username][module]
        if subtopic:
            if subtopic not in user_data:
                print(f"[Error] Subtopic '{subtopic}' not found in module '{module}'.")
                return
            done = user_data[subtopic]
            # find total
            total = None
            for cats in self.config[module].values():
                if subtopic in cats:
                    total = cats[subtopic]
                    break
            print(f"{subtopic}: {done} / {total}")
        else:
            print(f"Progress for user '{username}' in module '{module}':")
            for cat, subs in self.config[module].items():
                cat_done = sum(user_data[s] for s in subs)
                cat_total = sum(subs.values())
                print(f"  {cat} – {cat_done} / {cat_total}")
                for sub, total in subs.items():
                    done = user_data.get(sub, 0)
                    print(f"    * {sub}: {done} / {total}")


# ----- CLI Logic -----

def print_main_help():
    print("""
Available commands (main):
  create <username>            Create a new user
  list users                   Show all users
  go <username>                Enter user context
  reset <username>             Reset all progress for user
  reset <username> <module> <subtopic>   Reset specific subtopic for user
  help                         Show this help message
  exit                         Exit the program
""")


def user_context_loop(tracker, username):
    # Module selection
    while True:
        mod = input(f"[{username}] Select module (reading/listening) or 'back': ").strip().lower()
        if mod == 'back':
            return
        if mod not in tracker.config or not tracker.config[mod]:
            print(f"[Error] Module '{mod}' unavailable or not configured.")
            continue
        module = mod
        break

    print(f"-- Entered '{module}' for user '{username}'. Type 'help' for commands, 'back' to leave module.")
    while True:
        raw = input(f"[{username}:{module}]> ").strip()
        if not raw:
            continue
        parts = raw.split()
        cmd = parts[0].lower()
        if cmd == 'back':
            break
        if cmd == 'help':
            print("""
User commands:
  add <subtopic> <num>      Add completed count to a subtopic
  min <subtopic> <num>      Subtract count from a subtopic
  list [<subtopic>]         Show progress (all or one subtopic)
  reset <subtopic>          Reset a subtopic to zero
  help                      Show this help
  back                      Return to main menu
""")
            continue
        if cmd == 'add' and len(parts) >= 3:
            try:
                num = int(parts[-1])
            except ValueError:
                print("[Error] Last argument must be a number.")
                continue
            sub = " ".join(parts[1:-1])
            tracker.add_progress(username, module, sub, num)
            continue
        if cmd in ('min', 'subtract') and len(parts) >= 3:
            try:
                num = int(parts[-1])
            except ValueError:
                print("[Error] Last argument must be a number.")
                continue
            sub = " ".join(parts[1:-1])
            tracker.subtract_progress(username, module, sub, num)
            continue
        if cmd == 'list':
            if len(parts) == 1:
                tracker.print_progress(username, module)
            else:
                sub = " ".join(parts[1:])
                tracker.print_progress(username, module, sub)
            continue
        if cmd == 'reset' and len(parts) >= 2:
            sub = " ".join(parts[1:])
            tracker.reset_subtopic(username, module, sub)
            continue
        print("[Error] Unknown command. Type 'help' for help.")


def main():
    tracker = TOEFLTracker()
    print("TOEFL Progress Tracker\nType 'help' for commands.")
    while True:
        raw = input("> ").strip()
        if not raw:
            continue
        parts = raw.split()
        cmd = parts[0].lower()
        if cmd == 'exit' or cmd == 'quit':
            print("Bye!")
            sys.exit(0)
        if cmd == 'help':
            print_main_help()
            continue
        if cmd == 'create' and len(parts) == 2:
            tracker.create_user(parts[1])
            continue
        if cmd == 'list' and len(parts) == 2 and parts[1] == 'users':
            tracker.list_users()
            continue
        if cmd == 'go' and len(parts) == 2:
            username = parts[1]
            if tracker.ensure_user(username):
                user_context_loop(tracker, username)
            continue
        if cmd == 'reset' and len(parts) == 2:
            tracker.reset_user(parts[1])
            continue
        if cmd == 'reset' and len(parts) >= 4:
            username = parts[1]
            module = parts[2]
            sub = " ".join(parts[3:])
            tracker.reset_subtopic(username, module, sub)
            continue
        print("[Error] Unknown command. Type 'help' for help.")


if __name__ == '__main__':
    main()


