# auto_lingoleap_hybrid_mac.py
"""Hybrid 自动开号脚本：Selenium 仅负责启动浏览器，其余全部坐标+OCR
=======================================================================
需求回顾
--------
> *“我只说了一开始打开浏览器的时候使用我给的代码的部分，其他的全部用坐标去操作的”*

所以本版流程：
1. **Selenium** 负责：
   * 启动/关闭 Chrome（固定窗口 1280×900 @ (0,0)）
   * 打开第一个网址 `https://www.emailtick.com/`
2. **PyAutoGUI + Tesseract OCR** 负责：
   * 全部页面点击/输入操作（按你给定坐标）。
   * OCR 区域提取 6 位验证码。
3. 账号批量创建完毕后一次性写入 `account.txt`（文件头插入，入库次数自增）。

⚙️ 依赖
--------
```bash
brew install tesseract                                 # OCR
pip install pyautogui pytesseract pillow pyperclip selenium webdriver-manager
```

🚀 运行示例
-------------
```bash
python auto_lingoleap_hybrid_mac.py --count 2 \
       --driver /Users/<USER>/chromedriver-mac-arm64/chromedriver
```
*如省略 `--driver` 将由 webdriver‑manager 自动下载匹配版本。*

坐标表（默认 1280×900，100 % scaling）
---------------------------------------
可在 `COORD` 字典里调整。
```text
copy_email     871,396
email_field    926,571
pwd_field      928,676
sign_up_btn    958,863
check_mail     678,1002
ocr_region     597,561 – 780,589
code_field     859,721
verify_btn     935,823
delete_btn     869,576
confirm_delete 852,254
```

"""

from __future__ import annotations
import pdb
import argparse, random, string, time, re, subprocess, sys
from datetime import datetime
from pathlib import Path

import pyautogui as pag
from PIL import Image
import pytesseract, pyperclip

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager
from TOEFL.Tool.window import *



import datetime
import inspect
import os
import sys
OriginalPrint=print

def enhanced_print(*args, **kwargs):
    """
    增强版的 print 函数。

    在打印用户提供的内容之前，先输出当前时间、调用该函数的文件名和行号。
    它接受与内置 print 函数相同的参数。

    Args:
        *args: 传递给内置 print 函数的位置参数。
        **kwargs: 传递给内置 print 函数的关键字参数 (例如 sep, end, file, flush)。
    """
    caller_frame = None # 初始化为 None
    try:
        # 获取调用者的栈帧 (向上一层)
        # inspect.currentframe() 获取当前帧
        # .f_back 获取上一层 (即调用 enhanced_print 的地方)
        caller_frame = inspect.currentframe().f_back
        if caller_frame:
            # 从栈帧信息中提取所需内容
            frame_info = inspect.getframeinfo(caller_frame)
            timestamp = datetime.datetime.now()
            filename = os.path.basename(frame_info.filename) # 只获取文件名，不含路径
            lineno = frame_info.lineno

            # --- 格式化前缀 ---
            # 你可以根据需要调整时间格式，例如:
            # formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S.%f')[:-3] # 到毫秒
            formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S') # 到秒
            prefix = f"[{formatted_time}] [{filename}:{lineno}]"

            # --- 执行打印 ---
            # 1. 获取传递给 print 的 file 参数，默认为 sys.stdout
            output_file = kwargs.get('file', sys.stdout)
            # 2. 获取传递给 print 的 flush 参数，默认为 False
            flush = kwargs.get('flush', False)
            OriginalPrint("\n\n\n\n\n{:=^88s}".format("Split Line"))
            OriginalPrint("{:=^88s}".format("Split Line"))
            # 3. 先打印前缀，注意 end=' ' 在前缀后加空格，而不是换行
            OriginalPrint(prefix, end=' ', file=output_file, flush=flush)

            # 4. 打印用户传入的实际内容
            #    将 *args 和 **kwargs 传递给内置的 print 函数
            OriginalPrint(*args, **kwargs) # 注意：这里不需要再指定 file 和 flush，因为它们包含在 kwargs 里了
                                   # 如果 kwargs 中没有 file/flush, print 会使用默认值

        else:
            # 如果由于某种原因无法获取调用者帧信息，则回退到标准打印并添加错误提示
            OriginalPrint("[Enhanced Print Error: Could not get caller frame info]", *args, **kwargs)

    finally:
        # 这是一个好的实践，确保删除对帧对象的引用，以帮助垃圾回收
        # 尤其是在某些旧的 Python 版本或特定实现中可能有助于防止循环引用
        del caller_frame


print=enhanced_print

pag.FAILSAFE = True

# 坐标配置
COORD = {
    'copy_email': (871, 396),
    'email_field': (988, 465),
    'pwd_field':   (951, 592),
    'sign_up_btn': (989, 745), 
    "check_mail_region":(316, 714,728, 215),
    'check_mail1':  (689, 750), 
    'check_mail2':  (686, 811),
    'mai_point': (665, 731),
    'ocr_region':  (632, 568,151, 60),  # x1,y1,x2,y2
    'cc':(907, 565),
    'code_field':  (859, 721),
    'verify_btn':  (935, 823),
    'delete_btn':  (869, 576),
    'confirm_delete': (852, 254)
}

ACCOUNT_FILE = Path('account.txt')
MAIL_URL = 'https://www.emailtick.com/'
REG_URL  = 'https://app.lingoleap.ai/register'

# ---------- 辅助函数 ---------- #

def rand_pwd(n: int = 12):
    chars = string.ascii_letters + string.digits + string.punctuation
    return ''.join(random.choice(chars) for _ in range(n))


def click(pt, delay=0.2):
    pag.moveTo(*pt, duration=0.15)
    pag.click()
    time.sleep(delay)


def type_text(text, interval=0.03):
    pag.typewrite(text, interval=interval)


def open_url(url):
    pag.hotkey('command', 'l')
    type_text(url, interval=0.02)
    pag.press('enter')


def ocr_code(region):
    img = pag.screenshot(region=region)
    img.show()
    img.save("screenshot.png")  

    txt = pytesseract.image_to_string(img.convert('L'), config='--psm 7 digits')
    m = re.search(r'\d{6}', txt)
    if not m:
        raise RuntimeError(f'OCR 未识别到6位码: {txt!r}')
    return m.group(0)

# ---------- Selenium only for browser ---------- #

def setup_driver(path: str | None):
    opts = webdriver.ChromeOptions()
    opts.add_argument('--window-position=0,0')
    opts.add_argument('--window-size=1280,900')
    service = Service(executable_path=path) if path else Service(ChromeDriverManager().install())
    return webdriver.Chrome(service=service, options=opts)

# ---------- 主流程 ---------- #

def run_one(idx: int,driver ):


    code =""
    try:
       code = ocr_code(COORD['ocr_region'])
    except Exception as e:
        print(f"[{idx}] 未识别到验证码，跳过")
        pass
    # 如果是数字的话
    while not code.isdigit():
        try:
            code = ocr_code(COORD['ocr_region'])
            print(f"[{idx}] 识别验证码 {code}")

        except Exception as e:
            print(f"[{idx}] 未识别到验证码，跳过")
            pass
        time.sleep(8)    

    change_it("Please", "Lingoleap")
    click(COORD['code_field']); type_text(code)
    click(COORD['verify_btn'])
    print(f"[{idx}] 完成验证！")
    # 删除邮箱

    pag.hotkey('command', '1')
    print("command 1 ")
    pag.hotkey('command', '1')
    print("command 1 ")
    pag.hotkey('command', '1')
    print("command 1 ")
    # pag.press('pageup')
    # click(COORD['delete_btn'])
    # click(COORD['confirm_delete'])
    pdb.set_trace()
    time.sleep(3)

    return 0,0

# ---------- 文件入库 ---------- #

def last_no():
    if ACCOUNT_FILE.exists():
        m = re.search(r'第(\d+)次入库', ACCOUNT_FILE.read_text().splitlines()[0])
        return int(m.group(1)) if m else 0
    return 0


def save(batch):
    no = last_no() + 1
    header = f"{datetime.datetime.now():%Y-%m-%d} 第{no}次入库"
    content = [header] + [f"{e} {p}" for e,p in batch] + ['']
    old = ACCOUNT_FILE.read_text() if ACCOUNT_FILE.exists() else ''
    ACCOUNT_FILE.write_text('\n'.join(content) + old)
    print(f"✅ 写入 account.txt – 第{no}次入库 {len(batch)} 条")

# ---------- CLI ---------- #

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--count', type=int, default=1)
    ap.add_argument('--driver', type=str, help='chromedriver 路径，若省略自动匹配')
    args = ap.parse_args()

    driver = setup_driver(args.driver)
    try:
        # Selenium 打开第一个标签 (emailtick)
        driver.get(MAIL_URL)
        # 将控制权交给 PyAutoGUI 流程
        batch = []
        for i in range(1, args.count + 1):
            try:
                email, pwd = run_one(i, driver)
                # batch.append((email, pwd))
            except Exception as e:
                print(f"[ERROR] 第{i}个失败: {e}")
        # if batch:
            # save(batch)
    finally:
        driver.quit()
        print('浏览器已关闭')

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        sys.exit('\n用户中断')


