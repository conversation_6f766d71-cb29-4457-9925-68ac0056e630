import subprocess

# AppleScript 脚本，激活标题为 "Emailtick" 的窗口并切换到第二个标签页
apple_script = """
tell application "Google Chrome"
    -- 获取所有 Chrome 窗口
    set windowList to windows
    repeat with w in windowList
        -- 检查窗口标题
        if title of w contains "EmailTick" then
            -- 激活该窗口
            set activeWindow to w
            set index of activeWindow to 1  -- 激活窗口（确保该窗口在前）
            -- 切换到第二个标签页
            set active tab index of activeWindow to 2
            exit repeat  -- 找到窗口后跳出循环
        end if
    end repeat
end tell
"""

# 使用 osascript 执行 AppleScript
subprocess.run(["osascript", "-e", apple_script])

print("已切换到标题为 'Emailtick' 的窗口并切换到第二个标签页")
