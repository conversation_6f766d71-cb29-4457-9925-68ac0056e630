import os
from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.chrome.options import Options

chromedriver_path = "/Users/<USER>/Documents/APP/chromedriver-mac-arm64/chromedriver"   # brew 默认路径；根据实际调整

# ★ 正确生成 profile 目录
profile_dir = os.path.join(os.path.expanduser("~"), ".selenium_profile")
os.makedirs(profile_dir, exist_ok=True)   # 确保目录存在且有写权限

chrome_opts = Options()
chrome_opts.add_argument(f"--user-data-dir={profile_dir}")
chrome_opts.add_argument("--remote-debugging-port=9222")
chrome_opts.add_experimental_option("detach", True)

driver = webdriver.Chrome(
    service=Service(chromedriver_path),
    options=chrome_opts
)
driver.get("https://www.example.com")







