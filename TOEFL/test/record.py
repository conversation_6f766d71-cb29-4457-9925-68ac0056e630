
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
账号管理系统
===========
提供账号录入、状态管理、排序显示和数据导入导出功能。
"""

import os
import re
import csv
import sqlite3
import argparse
import datetime
import random
import string
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional, Union

# 数据库配置
DB_FILE = "accounts.db"
BACKUP_DIR = "backups"

# 定义颜色代码，用于命令行输出
class Colors:
    RED = '\033[91m'
    GREEN = '\033[92m'
    YELLOW = '\033[93m'
    BLUE = '\033[94m'
    BOLD = '\033[1m'
    UNDERLINE = '\033[4m'
    END = '\033[0m'

class AccountManager:
    def __init__(self, db_path: str = DB_FILE):
        """初始化账号管理器"""
        self.db_path = db_path
        self.conn = None
        self.cursor = None
        self.initialize_db()
    
    def initialize_db(self):
        """初始化数据库连接和表结构"""
        # 确保备份目录存在
        os.makedirs(BACKUP_DIR, exist_ok=True)
        
        # 连接数据库
        self.conn = sqlite3.connect(self.db_path)
        self.conn.row_factory = sqlite3.Row  # 使查询结果可通过列名访问
        self.cursor = self.conn.cursor()
        
        # 创建账号表
        self.cursor.execute('''
        CREATE TABLE IF NOT EXISTS accounts (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            email TEXT UNIQUE NOT NULL,
            password TEXT NOT NULL,
            invite_code TEXT,
            is_premium INTEGER DEFAULT 1,  -- 1=优质账号, 0=次品账号
            is_used INTEGER DEFAULT 0,     -- 0=未使用, 1=已使用
            created_at TEXT DEFAULT CURRENT_TIMESTAMP,
            last_updated TEXT DEFAULT CURRENT_TIMESTAMP
        )
        ''')
        self.conn.commit()
        
        # 检查是否需要添加邀请码字段（兼容旧数据库）
        self.cursor.execute("PRAGMA table_info(accounts)")
        columns = [column[1] for column in self.cursor.fetchall()]
        if "invite_code" not in columns:
            self.cursor.execute("ALTER TABLE accounts ADD COLUMN invite_code TEXT")
            self.conn.commit()
    
    def close(self):
        """关闭数据库连接"""
        if self.conn:
            self.conn.close()
    
    def is_premium_email(self, email: str) -> bool:
        """
        判断邮箱是否为优质账号
        优质账号: 前缀仅包含字母、数字和点号(.)
        """
        prefix = email.split('@')[0]
        return bool(re.match(r'^[a-zA-Z0-9.]+$', prefix))
    
    def add_account(self, email: str, password: str, invite_code: str = None) -> Tuple[bool, str]:
        """
        添加新账号
        参数:
            email: 邮箱地址
            password: 密码
            invite_code: 邀请码，如果为None则自动生成
        返回: (是否添加成功, 消息)
        """
        try:
            # 先检查邮箱是否已存在
            self.cursor.execute("SELECT email FROM accounts WHERE email = ?", (email,))
            existing = self.cursor.fetchone()
            if existing:
                return False, f"邮箱 {email} 已存在于系统中"
            
            # 如果没有提供邀请码，则自动生成
            if invite_code is None:
                invite_code = self.generate_invite_code()
            
            is_premium = 1 if self.is_premium_email(email) else 0
            current_time = datetime.datetime.now().isoformat()
            
            self.cursor.execute(
                "INSERT INTO accounts (email, password, invite_code, is_premium, created_at, last_updated) VALUES (?, ?, ?, ?, ?, ?)",
                (email, password, invite_code, is_premium, current_time, current_time)
            )
            self.conn.commit()
            quality = "优质账号" if is_premium else "次品账号"
            return True, f"账号添加成功 [{quality}] 邀请码: {invite_code}"
        except sqlite3.IntegrityError:
            # 邮箱已存在（虽然前面已检查，这里是双重保险）
            return False, f"邮箱 {email} 已存在于系统中"
        except Exception as e:
            return False, f"添加账号时发生错误: {str(e)}"
    
    def update_account_status(self, email: str, is_used: bool) -> bool:
        """
        更新账号使用状态
        返回: 是否更新成功
        """
        try:
            current_time = datetime.datetime.now().isoformat()
            self.cursor.execute(
                "UPDATE accounts SET is_used = ?, last_updated = ? WHERE email = ?",
                (1 if is_used else 0, current_time, email)
            )
            self.conn.commit()
            return self.cursor.rowcount > 0
        except Exception as e:
            print(f"更新状态失败: {e}")
            return False
    
    def batch_update_status(self, emails: List[str], is_used: bool) -> int:
        """
        批量更新账号状态
        返回: 成功更新的账号数量
        """
        try:
            current_time = datetime.datetime.now().isoformat()
            updated_count = 0
            
            for email in emails:
                self.cursor.execute(
                    "UPDATE accounts SET is_used = ?, last_updated = ? WHERE email = ?",
                    (1 if is_used else 0, current_time, email)
                )
                updated_count += self.cursor.rowcount
            
            self.conn.commit()
            return updated_count
        except Exception as e:
            print(f"批量更新失败: {e}")
            self.conn.rollback()
            return 0
    
    def get_accounts(self, 
                    filter_used: Optional[bool] = None, 
                    filter_premium: Optional[bool] = None,
                    search_term: str = "",
                    sort_by: str = "is_used,is_premium",
                    limit: int = 100,
                    offset: int = 0) -> List[Dict]:
        """
        获取账号列表
        参数:
            filter_used: 筛选已使用/未使用账号
            filter_premium: 筛选优质/次品账号
            search_term: 搜索关键词
            sort_by: 排序字段，逗号分隔
            limit: 返回数量限制
            offset: 分页偏移量
        """
        query = "SELECT * FROM accounts WHERE 1=1"
        params = []
        
        # 应用筛选条件
        if filter_used is not None:
            query += " AND is_used = ?"
            params.append(1 if filter_used else 0)
        
        if filter_premium is not None:
            query += " AND is_premium = ?"
            params.append(1 if filter_premium else 0)
        
        if search_term:
            query += " AND (email LIKE ? OR password LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern])
        
        # 应用排序
        if sort_by:
            sort_fields = []
            for field in sort_by.split(','):
                field = field.strip()
                if field in ['id', 'email', 'is_premium', 'is_used', 'created_at', 'last_updated']:
                    sort_fields.append(field)
            
            if sort_fields:
                query += f" ORDER BY {', '.join(sort_fields)}"
        
        # 应用分页
        query += " LIMIT ? OFFSET ?"
        params.extend([limit, offset])
        
        self.cursor.execute(query, params)
        return [dict(row) for row in self.cursor.fetchall()]
    
    def count_accounts(self, 
                      filter_used: Optional[bool] = None, 
                      filter_premium: Optional[bool] = None,
                      search_term: str = "") -> int:
        """获取符合条件的账号总数"""
        query = "SELECT COUNT(*) FROM accounts WHERE 1=1"
        params = []
        
        if filter_used is not None:
            query += " AND is_used = ?"
            params.append(1 if filter_used else 0)
        
        if filter_premium is not None:
            query += " AND is_premium = ?"
            params.append(1 if filter_premium else 0)
        
        if search_term:
            query += " AND (email LIKE ? OR password LIKE ?)"
            search_pattern = f"%{search_term}%"
            params.extend([search_pattern, search_pattern])
        
        self.cursor.execute(query, params)
        return self.cursor.fetchone()[0]
    
    def import_from_csv(self, file_path: str) -> Tuple[int, int]:
        """
        从CSV文件导入账号
        返回: (成功导入数量, 失败数量)
        """
        success_count = 0
        fail_count = 0
        
        try:
            with open(file_path, 'r', newline='', encoding='utf-8') as csvfile:
                reader = csv.reader(csvfile)
                header = next(reader, None)  # 尝试读取标题行
                
                # 确定列索引
                email_idx, pwd_idx, invite_idx = 0, 1, 2
                if header and len(header) >= 3:
                    # 如果有标题行，尝试找到对应的列
                    for i, col in enumerate(header):
                        if '邮箱' in col or 'email' in col.lower():
                            email_idx = i
                        elif '密码' in col or 'password' in col.lower():
                            pwd_idx = i
                        elif '邀请码' in col or 'invite' in col.lower():
                            invite_idx = i
                
                for row in reader:
                    if len(row) >= 2:
                        email = row[email_idx].strip()
                        password = row[pwd_idx].strip()
                        invite_code = row[invite_idx].strip() if len(row) > invite_idx else None
                        
                        if '@' in email and password:
                            success, _ = self.add_account(email, password, invite_code)
                            if success:
                                success_count += 1
                            else:
                                fail_count += 1
                        else:
                            fail_count += 1
            return success_count, fail_count
        except Exception as e:
            print(f"导入CSV失败: {e}")
            return success_count, fail_count
    
    def export_to_csv(self, file_path: str, 
                     filter_used: Optional[bool] = None,
                     filter_premium: Optional[bool] = None) -> int:
        """
        导出账号到CSV文件
        返回: 导出的账号数量
        """
        try:
            accounts = self.get_accounts(
                filter_used=filter_used,
                filter_premium=filter_premium,
                limit=10000  # 设置较大的限制以导出所有符合条件的账号
            )
            
            with open(file_path, 'w', newline='', encoding='utf-8') as csvfile:
                writer = csv.writer(csvfile)
                writer.writerow(['邮箱', '密码', '邀请码', '账号质量', '使用状态', '创建时间', '最后更新'])
                
                for account in accounts:
                    writer.writerow([
                        account['email'],
                        account['password'],
                        account.get('invite_code', ''),
                        '优质账号' if account['is_premium'] else '次品账号',
                        '已使用' if account['is_used'] else '未使用',
                        account['created_at'],
                        account['last_updated']
                    ])
            
            return len(accounts)
        except Exception as e:
            print(f"导出CSV失败: {e}")
            return 0
    
    def backup_database(self) -> str:
        """
        备份数据库
        返回: 备份文件路径
        """
        timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = os.path.join(BACKUP_DIR, f"accounts_backup_{timestamp}.db")
        
        try:
            # 确保连接已关闭
            self.conn.commit()
            
            # 复制数据库文件
            import shutil
            shutil.copy2(self.db_path, backup_path)
            return backup_path
        except Exception as e:
            print(f"备份失败: {e}")
            return ""
    
    def restore_database(self, backup_path: str) -> bool:
        """
        从备份恢复数据库
        返回: 是否恢复成功
        """
        try:
            # 关闭当前连接
            self.close()
            
            # 复制备份文件到主数据库
            import shutil
            shutil.copy2(backup_path, self.db_path)
            
            # 重新初始化连接
            self.initialize_db()
            return True
        except Exception as e:
            print(f"恢复失败: {e}")
            # 尝试重新连接
            self.initialize_db()
            return False

    def generate_invite_code(self, length: int = 8) -> str:
        """生成随机邀请码"""
        chars = string.ascii_uppercase + string.digits
        return ''.join(random.choice(chars) for _ in range(length))

    def update_account_field(self, email: str, field: str, value: Any) -> Tuple[bool, str]:
        """
        更新账号的任意字段
        参数:
            email: 邮箱地址
            field: 要更新的字段名
            value: 新的字段值
        返回: (是否更新成功, 消息)
        """
        allowed_fields = ['password', 'invite_code', 'is_premium', 'is_used']
        
        if field not in allowed_fields:
            return False, f"不允许更新字段: {field}"
        
        try:
            # 先检查账号是否存在
            self.cursor.execute("SELECT * FROM accounts WHERE email = ?", (email,))
            account = self.cursor.fetchone()
            if not account:
                return False, f"账号不存在: {email}"
            
            # 更新字段
            current_time = datetime.datetime.now().isoformat()
            self.cursor.execute(
                f"UPDATE accounts SET {field} = ?, last_updated = ? WHERE email = ?",
                (value, current_time, email)
            )
            self.conn.commit()
            
            if self.cursor.rowcount > 0:
                return True, f"成功更新 {email} 的 {field} 字段"
            else:
                return False, f"更新失败，可能没有变化"
        except Exception as e:
            return False, f"更新字段时发生错误: {str(e)}"


# 命令行界面
def display_accounts(accounts: List[Dict]):
    """显示账号列表"""
    if not accounts:
        print("没有找到符合条件的账号")
        return
    
    # 打印表头
    print(f"{'ID':<5} {'邮箱':<30} {'密码':<15} {'邀请码':<10} {'质量':<8} {'状态':<8} {'创建时间':<20}")
    print("-" * 100)
    
    # 打印账号信息
    for acc in accounts:
        quality = "优质账号" if acc['is_premium'] else "次品账号"
        status = "已使用" if acc['is_used'] else "未使用"
        created_at = acc['created_at'].split('T')[0] if 'T' in acc['created_at'] else acc['created_at']
        invite_code = acc.get('invite_code', '')
        
        print(f"{acc['id']:<5} {acc['email']:<30} {acc['password']:<15} {invite_code:<10} {quality:<8} {status:<8} {created_at:<20}")

def display_warning(message: str):
    """显示警告消息，使用颜色和符号使其更加醒目"""
    print(f"{Colors.YELLOW}{Colors.BOLD}⚠️ 警告: {message}{Colors.END}")

def display_success(message: str):
    """显示成功消息"""
    print(f"{Colors.GREEN}✅ {message}{Colors.END}")

def display_error(message: str):
    """显示错误消息"""
    print(f"{Colors.RED}❌ {message}{Colors.END}")

def interactive_menu(manager: AccountManager):
    """交互式菜单"""
    while True:
        print("\n===== 账号管理系统 =====")
        print("1. 添加新账号")
        print("2. 查看账号列表")
        print("3. 更新账号状态")
        print("4. 修改账号字段")
        print("5. 批量更新状态")
        print("6. 导入账号(CSV)")
        print("7. 导出账号(CSV)")
        print("8. 备份数据库")
        print("9. 恢复数据库")
        print("0. 退出系统")
        
        choice = input("\n请选择操作 [0-9]: ").strip()
        
        if choice == '0':
            break
        
        elif choice == '1':
            email = input("请输入邮箱: ").strip()
            if '@' not in email:
                display_error("无效的邮箱格式")
                continue
            
            password = input("请输入密码: ").strip()
            if not password:
                display_error("密码不能为空")
                continue
            
            invite_code = input("请输入邀请码 (留空自动生成): ").strip()
            invite_code = invite_code if invite_code else None
            
            success, message = manager.add_account(email, password, invite_code)
            if success:
                display_success(message)
            else:
                display_warning(message)
        
        elif choice == '2':
            print("\n筛选条件:")
            used_filter = input("使用状态 (1=已使用, 0=未使用, 空=全部): ").strip()
            premium_filter = input("账号质量 (1=优质账号, 0=次品账号, 空=全部): ").strip()
            search = input("搜索关键词 (空=不搜索): ").strip()
            
            filter_used = int(used_filter) if used_filter in ['0', '1'] else None
            filter_premium = int(premium_filter) if premium_filter in ['0', '1'] else None
            
            accounts = manager.get_accounts(
                filter_used=filter_used,
                filter_premium=filter_premium,
                search_term=search,
                sort_by="is_used,is_premium,id"
            )
            
            print(f"\n找到 {len(accounts)} 个账号:")
            display_accounts(accounts)
        
        elif choice == '3':
            email = input("请输入要更新的邮箱: ").strip()
            status = input("新状态 (1=已使用, 0=未使用): ").strip()
            
            if status not in ['0', '1']:
                print("无效的状态值")
                continue
            
            if manager.update_account_status(email, status == '1'):
                print("状态更新成功")
            else:
                print("状态更新失败，邮箱可能不存在")
        
        elif choice == '4':
            email = input("请输入要修改的账号邮箱: ").strip()
            if not email or '@' not in email:
                display_error("无效的邮箱格式")
                continue
            
            print("\n可修改的字段:")
            print("1. 密码 (password)")
            print("2. 邀请码 (invite_code)")
            print("3. 账号质量 (is_premium, 1=优质账号, 0=次品账号)")
            print("4. 使用状态 (is_used, 1=已使用, 0=未使用)")
            
            field_choice = input("请选择要修改的字段 [1-4]: ").strip()
            
            field_map = {
                '1': 'password',
                '2': 'invite_code',
                '3': 'is_premium',
                '4': 'is_used'
            }
            
            if field_choice not in field_map:
                display_error("无效的选择")
                continue
            
            field = field_map[field_choice]
            
            # 根据字段类型获取不同的输入
            if field in ['is_premium', 'is_used']:
                value_input = input(f"请输入新的值 (1=是, 0=否): ").strip()
                if value_input not in ['0', '1']:
                    display_error("无效的值，必须是 0 或 1")
                    continue
                value = int(value_input)
            else:
                value = input(f"请输入新的{field}值: ").strip()
                if not value:
                    display_error("值不能为空")
                    continue
            
            success, message = manager.update_account_field(email, field, value)
            if success:
                display_success(message)
            else:
                display_warning(message)
        
        elif choice == '5':
            print("批量更新状态 (每行一个邮箱，输入空行结束)")
            emails = []
            while True:
                line = input().strip()
                if not line:
                    break
                emails.append(line)
            
            if not emails:
                print("未输入任何邮箱")
                continue
            
            status = input("新状态 (1=已使用, 0=未使用): ").strip()
            if status not in ['0', '1']:
                print("无效的状态值")
                continue
            
            updated = manager.batch_update_status(emails, status == '1')
            print(f"成功更新 {updated} 个账号状态")
        
        elif choice == '6':
            file_path = input("请输入CSV文件路径: ").strip()
            if not os.path.exists(file_path):
                print("文件不存在")
                continue
            
            success, fail = manager.import_from_csv(file_path)
            print(f"导入完成: 成功 {success} 个, 失败 {fail} 个")
        
        elif choice == '7':
            file_path = input("请输入导出CSV文件路径: ").strip()
            
            used_filter = input("筛选使用状态 (1=已使用, 0=未使用, 空=全部): ").strip()
            premium_filter = input("筛选账号质量 (1=优质账号, 0=次品账号, 空=全部): ").strip()
            
            filter_used = int(used_filter) if used_filter in ['0', '1'] else None
            filter_premium = int(premium_filter) if premium_filter in ['0', '1'] else None
            
            count = manager.export_to_csv(file_path, filter_used, filter_premium)
            print(f"导出完成: 共 {count} 个账号")
        
        elif choice == '8':
            backup_path = manager.backup_database()
            if backup_path:
                print(f"备份成功: {backup_path}")
            else:
                print("备份失败")
        
        elif choice == '9':
            # 列出所有备份
            backups = sorted([f for f in os.listdir(BACKUP_DIR) if f.endswith('.db')])
            
            if not backups:
                print("没有找到备份文件")
                continue
            
            print("\n可用备份:")
            for i, backup in enumerate(backups):
                print(f"{i+1}. {backup}")
            
            choice = input("\n请选择要恢复的备份编号 (0=取消): ").strip()
            if not choice.isdigit() or int(choice) == 0:
                continue
            
            idx = int(choice) - 1
            if 0 <= idx < len(backups):
                backup_path = os.path.join(BACKUP_DIR, backups[idx])
                confirm = input(f"确定要恢复备份 {backups[idx]}? (y/n): ").strip().lower()
                
                if confirm == 'y':
                    if manager.restore_database(backup_path):
                        print("数据库恢复成功")
                    else:
                        print("数据库恢复失败")
            else:
                print("无效的选择")


def main():
    parser = argparse.ArgumentParser(description="账号管理系统")
    parser.add_argument('--db', type=str, default=DB_FILE, help="数据库文件路径")
    parser.add_argument('--no-color', action='store_true', help="禁用彩色输出")
    
    subparsers = parser.add_subparsers(dest='command', help='子命令')
    
    # 添加账号
    add_parser = subparsers.add_parser('add', help='添加新账号')
    add_parser.add_argument('email', help='邮箱地址')
    add_parser.add_argument('password', help='密码')
    add_parser.add_argument('--invite', type=str, help='邀请码 (可选)')
    
    # 更新账号状态
    update_parser = subparsers.add_parser('update', help='更新账号状态')
    update_parser.add_argument('email', help='邮箱地址')
    update_parser.add_argument('--used', type=int, choices=[0, 1], required=True, help='使用状态 (0=未使用, 1=已使用)')
    
    # 修改账号字段
    modify_parser = subparsers.add_parser('modify', help='修改账号字段')
    modify_parser.add_argument('email', help='邮箱地址')
    modify_parser.add_argument('--field', type=str, required=True, 
                              choices=['password', 'invite_code', 'is_premium', 'is_used'],
                              help='要修改的字段')
    modify_parser.add_argument('--value', type=str, required=True, help='新的字段值')
    
    # 列出账号
    list_parser = subparsers.add_parser('list', help='列出账号')
    list_parser.add_argument('--used', type=int, choices=[0, 1], help='筛选使用状态')
    list_parser.add_argument('--premium', type=int, choices=[0, 1], help='筛选账号质量')
    list_parser.add_argument('--search', type=str, default='', help='搜索关键词')
    list_parser.add_argument('--sort', type=str, default='is_used,is_premium', help='排序字段')
    
    # 导入CSV
    import_parser = subparsers.add_parser('import', help='从CSV导入账号')
    import_parser.add_argument('file', help='CSV文件路径')
    
    # 导出CSV
    export_parser = subparsers.add_parser('export', help='导出账号到CSV')
    export_parser.add_argument('file', help='CSV文件路径')
    export_parser.add_argument('--used', type=int, choices=[0, 1], help='筛选使用状态')
    export_parser.add_argument('--premium', type=int, choices=[0, 1], help='筛选账号质量')
    
    args = parser.parse_args()
    
    # 如果禁用颜色，清空颜色代码
    if args.no_color:
        for attr in dir(Colors):
            if not attr.startswith('__'):
                setattr(Colors, attr, '')
    
    manager = AccountManager(args.db)
    
    if args.command == 'add':
        if '@' not in args.email:
            display_error("无效的邮箱格式")
            return
        
        if not args.password:
            display_error("密码不能为空")
            return
        
        success, message = manager.add_account(args.email, args.password, args.invite)
        if success:
            display_success(message)
        else:
            display_warning(message)
    
    elif args.command == 'update':
        if manager.update_account_status(args.email, args.used):
            print("状态更新成功")
        else:
            print("状态更新失败，邮箱可能不存在")
    
    elif args.command == 'modify':
        # 处理布尔值字段
        value = args.value
        if args.field in ['is_premium', 'is_used']:
            if value not in ['0', '1']:
                display_error(f"字段 {args.field} 的值必须是 0 或 1")
                return
            value = int(value)
        
        success, message = manager.update_account_field(args.email, args.field, value)
        if success:
            display_success(message)
        else:
            display_warning(message)
    
    elif args.command == 'list':
        accounts = manager.get_accounts(
            filter_used=args.used,
            filter_premium=args.premium,
            search_term=args.search,
            sort_by=args.sort
        )
        display_accounts(accounts)
    
    elif args.command == 'import':
        success, fail = manager.import_from_csv(args.file)
        print(f"导入完成: 成功 {success} 个, 失败 {fail} 个")
    
    elif args.command == 'export':
        count = manager.export_to_csv(args.file, args.used, args.premium)
        print(f"导出完成: 共 {count} 个账号")
    
    else:
        interactive_menu(manager)
    
    manager.close()


if __name__ == "__main__":
    main()

