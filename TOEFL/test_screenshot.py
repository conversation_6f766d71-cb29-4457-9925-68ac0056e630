from __future__ import annotations
import pdb
import argparse, random, string, time, re, subprocess, sys
from datetime import datetime
from pathlib import Path

import pyautogui as pag
from PIL import Image
import pytesseract, pyperclip

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

COORD = {
    'copy_email': (871, 396),
    'email_field': (988, 465),
    'pwd_field':   (951, 592),
    'sign_up_btn': (989, 745), 
    "test":(602, 752,164, 112),
    'check_mail1':  (689, 750), 
    'check_mail2':  (686, 811),
    'ocr_region':  (597, 561, 780, 589),  # x1,y1,x2,y2
    'code_field':  (859, 721),
    'verify_btn':  (935, 823),
    'delete_btn':  (869, 576),
    'confirm_delete': (852, 254)
}

#鼠标移动到 check email
# pag.moveTo(*COORD['test'], duration=0.15)
# pag.click()
# time.sleep(7)
# exit()


img = pag.screenshot(region=COORD['test'])
img.show()
img.save("screenshot.png")  