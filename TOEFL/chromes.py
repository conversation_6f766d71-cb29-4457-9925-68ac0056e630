# auto_lingoleap_hybrid_mac.py
"""Hybrid 自动开号脚本：Selenium 仅负责启动浏览器，其余全部坐标+OCR
=======================================================================
与上一版相比：
1. Selenium 现在显式调用 **Google Chrome**（非 Chromium）
2. CLI 新增 --chrome-binary 参数；如不填则使用默认路径
其余逻辑完全相同。
"""

from __future__ import annotations
import argparse, random, string, time, re, sys, os, inspect, datetime
from pathlib import Path

import pyautogui as pag
from PIL import Image
import pytesseract, pyperclip

from selenium import webdriver
from selenium.webdriver.chrome.service import Service
from webdriver_manager.chrome import ChromeDriverManager

# -------------------- 全局常量 --------------------

# ==== CHANGED: 默认 Google Chrome 程序路径（macOS）
DEFAULT_CHROME_BINARY = "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome"

# 如果你放在别的地方，可以 CLI 里用 --chrome-binary 覆盖
DEFAULT_DRIVER_PATH = "/Users/<USER>/Documents/APP/chromedriver-mac-arm64/chromedriver"

profile_dir = os.path.join(os.path.expanduser("~"), ".selenium_profile")
os.makedirs(profile_dir, exist_ok=True)          # 确保目录存在

# -------------------- 增强版 print --------------------

OriginalPrint = print
def enhanced_print(*args, **kwargs):
    caller = inspect.currentframe().f_back
    if caller:
        ts   = datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        file = os.path.basename(caller.f_code.co_filename)
        line = caller.f_lineno
        prefix = f"[{ts}] [{file}:{line}]"
        OriginalPrint("\n{:=^88s}".format("Split Line"))
        OriginalPrint(prefix, end=' ')
    OriginalPrint(*args, **kwargs)
print = enhanced_print

# -------------------- PyAutoGUI 设置 --------------------
pag.FAILSAFE = True

# 坐标配置（保持原样）
COORD = {
    'copy_email': (871, 396),
    'email_field': (988, 465),
    'pwd_field':   (951, 592),
    'sign_up_btn': (989, 745),
    "check_mail_region": (316, 714, 728, 215),
    'check_mail1':  (689, 750),
    'check_mail2':  (686, 811),
    'mai_point':    (665, 731),
    'ocr_region':   (632, 568, 151, 60),
    'cc':           (907, 565),
    'code_field':   (817, 567),
    'verify_btn':   (1000, 674),
    'delete_btn':   (882, 550),
    'confirm_delete': (858, 233)
}

ACCOUNT_FILE = Path('account.txt')
MAIL_URL = 'https://www.emailtick.com/'
REG_URL  = 'https://app.lingoleap.ai/register'

# -------------------- 小工具 --------------------

def rand_pwd(n: int = 12):
    chars = string.ascii_letters + string.digits + string.punctuation
    return ''.join(random.choice(chars) for _ in range(n))

def click(pt, delay=0.2):
    pag.moveTo(*pt, duration=0.15); pag.click(); time.sleep(delay)

def type_text(text, interval=0.03):
    pag.typewrite(text, interval=interval)

def open_url(url):
    pag.hotkey('command', 'l'); type_text(url, 0.02); pag.press('enter')

def ocr_code(region):
    img = pag.screenshot(region=region)
    txt = pytesseract.image_to_string(img.convert('L'), config='--psm 7 digits')
    m = re.search(r'\d{6}', txt)
    if not m: raise RuntimeError(f'OCR 未识别到6位码: {txt!r}')
    return m.group(0)

# -------------------- Selenium 部分 --------------------

def setup_driver(driver_path: str|None, chrome_binary: str|None):
    opts = webdriver.ChromeOptions()
    opts.add_argument('--window-position=0,0')
    opts.add_argument('--window-size=1280,900')
    opts.add_argument(f'--user-data-dir={profile_dir}')     # 复用同一用户目录
    # ==== CHANGED: 指定 Google Chrome 二进制文件 ====
    if chrome_binary:
        opts.binary_location = chrome_binary
    else:
        opts.binary_location = DEFAULT_CHROME_BINARY
    # ---------------------------------------------------
    service = Service(executable_path=driver_path) if driver_path else Service(ChromeDriverManager().install())
    return webdriver.Chrome(service=service, options=opts)

# -------------------- 主流程 --------------------

def run_one(idx: int, driver):
    print(f"\n[{idx}] 打开 emailtick…")
    open_url(MAIL_URL); time.sleep(3)

    click(COORD['copy_email'])
    email = pyperclip.paste().strip()
    if '@' not in email: raise RuntimeError('剪贴板无邮箱')
    pwd = rand_pwd()
    print(f"[{idx}] 邮箱 {email}")

    # 注册
    print(f"[{idx}] 打开注册页…")
    pag.hotkey('command', 't'); time.sleep(0.3); open_url(REG_URL); time.sleep(5)
    click(COORD['email_field']); type_text(email)
    click(COORD['pwd_field']);   type_text(pwd)
    click(COORD['sign_up_btn']); time.sleep(1)

    # 反复检查邮件
    pag.hotkey('command', '1')
    while True:
        img = pag.screenshot(region=COORD['check_mail_region'])
        txt = pytesseract.image_to_string(img.convert('L'), config='--psm 7')
        if 'lingoleap' in txt.lower(): break
        click(COORD['check_mail1']); click(COORD['check_mail1']); time.sleep(7)
        click(COORD['check_mail2']); time.sleep(7)

    click(COORD['mai_point'])
    cur = driver.current_url
    while driver.current_url == cur:
        click(COORD['mai_point']); time.sleep(4)
    pag.press("pageup"); pag.press('pagedown'); time.sleep(2)

    while True:
        try:
            code = ocr_code(COORD['ocr_region'])
            print(f"[{idx}] 识别验证码 {code}")
            if code.isdigit(): break
        except Exception:
            print(f"[{idx}] 未识别到验证码，重试…")
        time.sleep(5)

    handles = driver.window_handles
    driver.switch_to.window(handles[1])
    click(COORD['code_field']); type_text(code); click(COORD['verify_btn'])
    print(f"[{idx}] 完成验证！")

    # 删除邮箱
    time.sleep(3); driver.switch_to.window(handles[0]); pag.press('pageup')
    click(COORD['delete_btn']); click(COORD['confirm_delete']); time.sleep(3)

    return email, pwd

# -------------------- 文件入库 --------------------

def last_no():
    if ACCOUNT_FILE.exists():
        m = re.search(r'第(\d+)次入库', ACCOUNT_FILE.read_text().splitlines()[0])
        return int(m.group(1)) if m else 0
    return 0

def save(batch):
    no = last_no() + 1
    header = f"{datetime.datetime.now():%Y-%m-%d} 第{no}次入库"
    content = [header] + [f"{e} {p}" for e,p in batch] + ['']
    old = ACCOUNT_FILE.read_text() if ACCOUNT_FILE.exists() else ''
    ACCOUNT_FILE.write_text('\n'.join(content) + old)
    print(f"✅ 写入 account.txt – 第{no}次入库 {len(batch)} 条")

# -------------------- CLI --------------------

def main():
    ap = argparse.ArgumentParser()
    ap.add_argument('--count', type=int, default=1)
    ap.add_argument('--driver', type=str, default=DEFAULT_DRIVER_PATH, help='ChromeDriver 路径')
    ap.add_argument('--chrome-binary', type=str, help='Google Chrome 程序路径')
    args = ap.parse_args()

    driver = setup_driver(args.driver, args.chrome_binary)
    try:
        driver.get(MAIL_URL)
        batch = []
        for i in range(1, args.count + 1):
            try:
                batch.append(run_one(i, driver))
            except Exception as e:
                print(f"[ERROR] 第{i}个失败: {e}")
        if batch: save(batch)
    finally:
        driver.quit(); print('浏览器已关闭')

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        sys.exit('\n用户中断')