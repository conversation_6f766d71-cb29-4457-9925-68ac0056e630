import subprocess
import shlex
import datetime
import inspect
import os
import sys

# 用于增强的打印函数
OriginalPrint = print

def enhanced_print(*args, **kwargs):
    caller_frame = None
    try:
        caller_frame = inspect.currentframe().f_back
        if caller_frame:
            frame_info = inspect.getframeinfo(caller_frame)
            timestamp = datetime.datetime.now()
            filename = os.path.basename(frame_info.filename)
            lineno = frame_info.lineno
            formatted_time = timestamp.strftime('%Y-%m-%d %H:%M:%S')
            prefix = f"[{formatted_time}] [{filename}:{lineno}]"
            output_file = kwargs.get('file', sys.stdout)
            flush = kwargs.get('flush', False)
            OriginalPrint("\n\n\n\n\n{:=^88s}".format("Split Line"))
            OriginalPrint("{:=^88s}".format("Split Line"))
            OriginalPrint(prefix, end=' ', file=output_file, flush=flush)
            OriginalPrint(*args, **kwargs)
        else:
            OriginalPrint("[Enhanced Print Error: Could not get caller frame info]", *args, **kwargs)
    finally:
        del caller_frame

# 替换默认 print 函数为 enhanced_print
print = enhanced_print

# 使用 AppleScript 唤醒 Chrome 窗口
def bring_chrome_to_front(window_title):
    script = f'''
    tell application "Google Chrome"
        set windowList to windows
        repeat with w in windowList
            set wTitle to the name of w
            if wTitle contains "{window_title}" then
                set index of w to 1  -- 将找到的窗口置于最前
                activate
                return
            end if
        end repeat
    end tell
    '''
    subprocess.run(['osascript', '-e', script])

# 用 chrome-cli 查找窗口
def get_chrome_window_by_title(title):
    out = subprocess.check_output("chrome-cli list windows", shell=True, text=True)
    print(out)
    for line in out.splitlines():
        print(title.lower())
        print(line.lower())
        if title.lower() in line.lower():  # 忽略大小写进行匹配
            window_id = line.split()[0].strip("[]")
            return window_id
    return None

# 切换标签并唤醒 Chrome
def switch_to_tab(window_id, tab_title):
    try:
        # 激活 Chrome 窗口
        bring_chrome_to_front("EmailTick")  # 唤醒并激活包含 "EmailTick" 的窗口

        out = subprocess.check_output(f"chrome-cli list tabs -w {window_id}", shell=True, text=True)
        print("当前标签列表：\n", out)  # 打印当前窗口下的所有标签
        tab_id = None
        for line in out.splitlines():
            if tab_title.lower().strip() == line.split()[1].lower().strip():  # 根据实际标题修改
            
                tab_id = line.split()[0].strip("[]")
                print(f"找到匹配标签：{tab_id}")
                break
        # for idx, line in enumerate(out.splitlines()):
        #     print(f"{idx}: {line}")
        #     if idx == tab_index:
        #         tab_id = line.split()[0].strip("[]")
        #         print(f"找到匹配标签：{tab_id}")
        #         break
        
        if tab_id:
            # 激活窗口并切换到目标标签
            subprocess.run(shlex.split(f"chrome-cli activate -t {tab_id}"))
            print(f"✅ 已切换到窗口 {window_id} 的第 {tab_id} 个标签")
        else:
            print("❌ 未找到匹配的标签")
    except Exception as e:
        print(f"❌ 切换标签时出错: {e}")
def change_it(window, tab_title):
    window_id = get_chrome_window_by_title(window)
    print(window_id)
    if window_id:
        print(f"找到窗口：{window_id}")
        switch_to_tab(window_id, tab_title)  # 切换到第二个标签
        
    else:
        print("❌ 未找到包含 {window} 的窗口")

if __name__ == "__main__":
    change_it("Please", "Lingoleap")